import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { OrderInvoiceQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { PostalGrpcService } from '../../../postal/service/postal.grpc.service';
import {
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Metadata } from '@grpc/grpc-js';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigurationGrpcService } from '../../services/configuration.grpc.service';

import puppeteer from 'puppeteer';
import { PackageService } from '../../../internal-client/services';
import { lastValueFrom } from 'rxjs';
import { toDateHuman } from '@app/common';
import * as https from 'https';
import * as http from 'http';

interface InvoiceResponse {
  buffer: Buffer;
  filename: string;
  contentType: string;
}

@QueryHandler(OrderInvoiceQuery)
export class OrderInvoiceHandler implements IQueryHandler<OrderInvoiceQuery> {
  private static browserInstance: any = null; // Static browser instance untuk reuse

  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
    private postalGrpc: PostalGrpcService,
    private pkgGrpc: PackageService,
    private readonly mailerService: MailerService,
    private readonly configGrpc: ConfigurationGrpcService,
  ) {}

  // Method untuk cleanup browser instance yang tidak responsive
  private static async cleanupBrowserInstance() {
    if (OrderInvoiceHandler.browserInstance) {
      try {
        console.log('[Cleaning up browser instance]...');
        await OrderInvoiceHandler.browserInstance.close();
        console.log('[Browser instance closed]');
      } catch (error) {
        console.error('[Error closing browser instance]:', error);
      } finally {
        OrderInvoiceHandler.browserInstance = null;
      }
    }
  }

  // Method untuk convert image URL ke base64
  private async imageToBase64(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const protocol = url.startsWith('https:') ? https : http;

      protocol
        .get(url, (response) => {
          if (response.statusCode !== 200) {
            reject(new Error(`Failed to fetch image: ${response.statusCode}`));
            return;
          }

          const chunks: Buffer[] = [];
          response.on('data', (chunk) => chunks.push(chunk));
          response.on('end', () => {
            const buffer = Buffer.concat(chunks);
            const mimeType = response.headers['content-type'] || 'image/webp';
            const base64 = buffer.toString('base64');
            resolve(`data:${mimeType};base64,${base64}`);
          });
        })
        .on('error', (error) => {
          reject(error);
        });
    });
  }

  async execute(Query: OrderInvoiceQuery): Promise<InvoiceResponse> {
    const { id, user } = Query;
    try {
      const item = await this.prisma.order.findFirst({
        where: {
          AND: [
            {
              property: {
                users: {
                  some: {
                    user: {
                      id: user.id,
                    },
                  },
                },
              },
            },
            { id: id },
          ],
        },
        include: {
          details: {
            include: {
              orderPackageDetail: true,
            },
            orderBy: { createdAt: 'desc' },
          },
          payment: true,
          property: true,
        },
      });

      if (!item) {
        throw new NotFoundException('Order not found');
      }

      const meta = new Metadata();
      const packageDetail = item.details.find(
        (detail) => detail.itemType === 'PLAN',
      );

      if (!packageDetail) {
        throw new NotFoundException('Package details not found');
      }

      try {
        const [propertyType, postal, pkg] = await Promise.all([
          lastValueFrom(
            this.propertyTypeGrpc.getOnePropertyType({
              id: item.property.propertyTypeId,
            }),
          ),
          lastValueFrom(
            this.postalGrpc.getPostal({ id: item.property.postalId }, meta),
          ),
          lastValueFrom(
            this.pkgGrpc.client.getOnePackage(
              {
                id: packageDetail.itemId,
                orderId: item.property.id,
              },
              meta,
            ),
          ),
        ]);

        // Process tax items safely
        let taxItems = [];
        try {
          if (typeof packageDetail.taxItems === 'string') {
            taxItems = JSON.parse(packageDetail.taxItems);
          } else if (Array.isArray(packageDetail.taxItems)) {
            taxItems = packageDetail.taxItems;
          } else if (typeof packageDetail.tax === 'string') {
            taxItems = JSON.parse(packageDetail.tax || '[]');
          }
        } catch (error) {
          console.error('Error parsing tax items:', error);
          taxItems = [];
        }

        // Ensure taxItems is always an array
        if (!Array.isArray(taxItems)) {
          console.warn('taxItems is not an array, defaulting to empty array');
          taxItems = [];
        }

        const taxes = taxItems.map((tax) => {
          if (tax.name === 'PPh23') {
            return {
              ...tax,
              value:
                Number(packageDetail.price) *
                packageDetail.qty *
                (Number(tax.nominal) / 100),
            };
          }
          return tax;
        });

        const subTotalafter =
          Number(packageDetail?.price) * packageDetail.qty -
          Number(packageDetail.discount || 0);

        const ttlPrice = String(
          Number(subTotalafter) + Number(packageDetail?.tax || 0),
        );

        // Convert images to base64 dengan fallback untuk dev environment
        console.log('[Converting images to base64]...');
        const startTime = Date.now();

        // Fallback SVG images untuk dev environment
        const fallbackLogo =
          'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwIiBoZWlnaHQ9IjYwIiB2aWV3Qm94PSIwIDAgMTgwIDYwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxODAiIGhlaWdodD0iNjAiIGZpbGw9IiNmMGYwZjAiLz48dGV4dCB4PSI5MCIgeT0iMzUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzMzMzMyI+VmVsb2RpdmE8L3RleHQ+PC9zdmc+';
        const fallbackSignature =
          'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwIiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMTgwIDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxODAiIGhlaWdodD0iODAiIGZpbGw9IiNmOWY5ZjkiLz48dGV4dCB4PSI5MCIgeT0iNDUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzY2NjY2NiI+U2lnbmF0dXJlPC90ZXh0Pjwvc3ZnPg==';
        const fallbackFooter =
          'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDEwMCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSI1MCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iMzAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMCIgZmlsbD0iIzk5OTk5OSI+Rm9vdGVyPC90ZXh0Pjwvc3ZnPg==';

        const [logoBase64, signedNameBase64, footerImageBase64] =
          await Promise.all([
            this.imageToBase64(
              'https://bucket.velodiva.com/membership-storage/static-assets/velodiva_pink.webp',
            ).catch((err) => {
              console.error(
                '[Logo conversion failed, using fallback]:',
                err.message,
              );
              return fallbackLogo;
            }),
            this.imageToBase64(
              'https://bucket.velodiva.com/membership-storage/static-assets/IMG_3711.webp',
            ).catch((err) => {
              console.error(
                '[Signature conversion failed, using fallback]:',
                err.message,
              );
              return fallbackSignature;
            }),
            this.imageToBase64(
              'https://bucket.velodiva.com/membership-storage/static-assets/footer_invoice_1.webp',
            ).catch((err) => {
              console.error(
                '[Footer conversion failed, using fallback]:',
                err.message,
              );
              return fallbackFooter;
            }),
          ]);

        const conversionTime = Date.now() - startTime;
        console.log(
          `[Images converted to base64 successfully] Time: ${conversionTime}ms`,
        );

        const htmlContent = `
    <html lang="">
    <head>
      <title>${item.invoice}</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
        @media print {
          @page {
            margin: 0;
          }
          * {
            font-family: 'Poppins', sans-serif;
          }
          body {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          .footer-image {
            width: 100%;
            margin-top: 50px;
            text-align: center;
            page-break-inside: avoid;
            clear: both;
          }
          .footer-image img {
            width: 100% !important;
            height: auto !important;
            display: block !important;
            max-width: 100% !important;
            margin: 0 auto !important;
          }
          .keterangan {
            position: fixed;
            bottom: 31%;
            left: 0;
            right: 0;
            height: auto;
            text-align: center;
          }
          .first-uppercase:first-letter {
            text-transform: uppercase;
          }
        }
      </style>
    </head>
    <body>
      <div style="padding: 12px;">
        <div style="max-width: 1000px; background-color: white;">
          <div style="display: flex; align-items: start; gap: 32px;">
            <div>
              <img id="logo" src="${logoBase64}" alt="Velodiva" style="width: 180px;">
            </div>
            <div style="display: flex; flex-direction: column;">
              <span style="font-weight: 800; font-size: 22px;">INFORMASI PAKET BERLANGGANAN VELODIVA</span>
              <span style="font-size: 22px; font-style: italic;">VELODIVA SUBSCRIPTION BILLING STATEMENT</span>
            </div>
          </div>
          <div style="width: 100%; height: 2px; margin-top: 16px; margin-bottom: 16px; background-color: #9ca3af;" />

          <div style="width: 100%; display: grid; grid-template-columns: 30% 70%; align-items: start; padding: 4px;">
            <div style="font-size: 10px;">
              <div style="display: flex; flex-direction: column;">
                <span style="font-weight: 800;">
                  Kepada <i>To</i>
                </span>
                <span style="font-weight: 400; margin-bottom: 6px;">
                </span>
                <span style="font-weight: 400;">
                  ${item.property.companyName}
                </span>
                <span style="font-weight: 400;">
                  ${item.property.address}
                </span>
                <span style="font-weight: 400;">
                  ${postal.city} 
                </span>
                <span style="font-weight: 400;">
                  ${postal.code}
                </span>
              </div>
            </div>
            <table style="width: 100%; font-size: 10px;" cellpadding="0" cellspacing="0">
              <tr>
                <td style="font-weight: 800; padding: 0; width: 30%;; vertical-align: top">
                  Nomor Faktur
                </td>
                <td style="font-style: italic; width: 30%; vertical-align: top">
                  Invoice Number
                </td>
                <td style="font-weight: 800; width: 40%; vertical-align: top">
                  : ${item.invoice}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Tanggal Faktur
                </td>
                <td style="font-style: italic; vertical-align: top">
                  Invoice Date
                </td>
                <td style="font-weight: 800; vertical-align: top">
                  : ${toDateHuman(item.payment?.paidAt)}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  NPWP / NIK
                </td>
                <td style="font-style: italic; vertical-align: top">
                  NPWP / NIK
                </td>
                <td style="font-weight: 800; vertical-align: top">
                  : ${this.formatNumberToFourGroups(item.property.npwp)}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Alamat NPWP
                </td>
                <td style="font-style: italic; vertical-align: top;">
                  NPWP Address
                </td>
                <td style="font-weight: 800; vertical-align: top;">
                  : ${item.property.billingAddress}
                </td>
              </tr>
            </table>
          </div>
          <div style="width: 100%; display: grid; grid-template-columns: 30% 70%; align-items: start; padding: 4px;">
            <div style="font-size: 10px;">

            </div>
            <table style="width: 100%; font-size: 10px;" cellpadding="0" cellspacing="0">
              <tr>
                <td style="font-weight: 800; padding: 0; width: 30%;; vertical-align: top">
                  Email Pelanggan
                </td>
                <td style="font-style: italic; width: 30%; vertical-align: top">
                  Customer Email
                </td>
                <td style="font-weight: 800; width: 40%; vertical-align: top">
                  : ${item.property.companyEmail}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Nomor ID Pelanggan
                </td>
                <td style="font-style: italic; vertical-align: top">
                  Customer ID
                </td>
                <td style="font-weight: 800; vertical-align: top">
                  : ${item.property.cid}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Nama Perusahaan
                </td>
                <td style="font-style: italic; vertical-align: top;">
                  Company Name
                </td>
                <td style="font-weight: 800; vertical-align: top;">
                  : ${item.property.companyName}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Merk Dagang
                </td>
                <td style="font-style: italic; vertical-align: top;">
                  Brand
                </td>
                <td style="font-weight: 800; vertical-align: top;">
                  : ${item.property.brandName}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Industri
                </td>
                <td style="font-style: italic; vertical-align: top;">
                  Industry
                </td>
                <td style="font-weight: 800; vertical-align: top;">
                  : ${propertyType.name}
                </td>
              </tr>
            </table>
          </div>
          <div style="margin-top: 8px;">
            <table style="border: 1px solid black; width: 100%; border-collapse: collapse; font-size: 10px;">
              <tr style="background-color: #666;">
                <td colspan="6" style="text-align: center; color: white; font-weight: 800;">
                  BIAYA BERLANGGANAN /<span style="font-weight: 800; font-style: italic;">SUBSCRIPTION FEE</span>
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: center;" rowspan="${8 + taxes.length}">
                  <i>Music Player</i>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  Nomor ID Pelanggan
                </td>
                <td style="border: 1px solid black; padding: 4px; font-style: italic;">
                  Customer ID
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;" colspan="3">
                  ${item.property.cid}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  Periode Pembayaran
                </td>
                <td style="border: 1px solid black; padding: 4px; font-style: italic;">
                  Payment Term
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;" colspan="3">
                  ${packageDetail.duration === 'monthly' ? 'Bulanan' : 'Tahunan'}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  Jangka Waktu Paket
                </td>
                <td style="border: 1px solid black; padding: 4px; font-style: italic;">
                  Subscription Term
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  ${toDateHuman(pkg.activeAt, true)}
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: center;">
                  s/d
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  ${toDateHuman(pkg.expiredAt, true)}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span style="font-weight: 800; text-align: center;">Deskripsi Layanan</span>
                    <span style="font-weight: 800; text-align: center; font-style: italic;">Item Description</span>
                  </div>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span style="font-weight: 800; text-align: center;">Jumlah Item</span>
                    <span style="font-weight: 800; text-align: center; font-style: italic;">Total Item</span>
                  </div>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span style="font-weight: 800; text-align: center;">Harga per Item</span>
                    <span style="font-weight: 800; text-align: center; font-style: italic;">Price per Item</span>
                  </div>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span style="font-weight: 800; text-align: center;">Diskon</span>
                    <span style="font-weight: 800; text-align: center; font-style: italic;">Discount</span>
                  </div>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span style="font-weight: 800; text-align: center;">Total</span>
                    <span style="font-weight: 800; text-align: center; font-style: italic;">Total</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td style="border: 1px solid black; padding: 4px;">
                  ${item.name}
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: center;">
                  ${packageDetail.qty}
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(packageDetail.price))}
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(packageDetail.discount))}
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(subTotalafter))}
                </td>
              </tr>
              <tr>
                <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                  SUB TOTAL
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(subTotalafter))}
                </td>
              </tr>
              <tr>
                <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                  DPP Nilai Lain
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(subTotalafter) * (11 / 12))}
                </td>
              </tr>
              ${taxes.map(
                (tax) => `
                <tr>
                  <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                    ${tax.name} (${tax.nominal}%)
                  </td>
                  <td style="border: 1px solid black; padding: 4px; text-align: end;">
                    Rp ${this.numberToLocaleStringID(Number(tax.value ?? 0))}
                  </td>
                </tr>
              `,
              )}
              <tr>
                <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                  TOTAL PEMBAYARAN <span style="font-style: italic; font-weight: 800;">TOTAL PAYMENT</span>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(ttlPrice))}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: center;">
                  <div style="display: flex; flex-direction: column;">
                    <span>Terbilang</span>
                    <span style="font-style: italic;">In Word</span>
                  </div>
                </td>
                <td colspan="5" style="border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column;">
                    <span class="first-uppercase">${this.numberToWords(Number(ttlPrice), 'id')} rupiah</span>
                    <span class="first-uppercase" style="font-style: italic;">${this.numberToWords(Number(ttlPrice), 'en')} rupiahs</span>
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <div style="display: grid; grid-template-columns: 60% 40%; align-items: center; margin-right: 10%;">
            <div>

            </div>
            <div style=";font-size: 10px; display: flex; flex-direction: column; align-items: end;">
              <img src="${signedNameBase64}" alt="Direktur Keuangan" style="max-width: 180px;" />
              <span style="text-align: center; margin-right: 36px;">Direktur Keuangan</span>
            </div>
          </div>
          <div class="keterangan" style="font-size: 10px;">
            Faktur ini merupakan bukti pembayaran lunas yang sah
            <br />
            <i>This invoice serves as an official receipt and proof of full payment.</i>
          </div>

          <!-- Footer Image -->
          <div class="footer-image">
            <img id="footer-image" src="${footerImageBase64}" alt="Footer Image">
          </div>

        </div>
      </div>
    </body>
    </html>
  `;

        const year = new Date().getFullYear();

        let bccEmails = [];
        try {
          const bccEmailsResponse = await this.configGrpc.getBccEmails();
          bccEmails = Array.isArray(bccEmailsResponse?.emails)
            ? bccEmailsResponse.emails
            : [];
        } catch (error) {
          console.error('Error fetching BCC emails:', error);
        }

        console.log('[Generating Pdf]...');
        let browser = null;
        let page = null;
        let retryCount = 0;
        const maxRetries = 3;

        try {
          // Reuse browser instance jika masih ada dan connected
          if (
            !OrderInvoiceHandler.browserInstance ||
            !OrderInvoiceHandler.browserInstance.isConnected()
          ) {
            // Cleanup existing instance if it exists but not connected
            if (OrderInvoiceHandler.browserInstance) {
              await OrderInvoiceHandler.cleanupBrowserInstance();
            }
            // Retry browser launch with exponential backoff
            while (retryCount < maxRetries) {
              try {
                console.log(
                  `[Launching new browser instance] Attempt ${retryCount + 1}/${maxRetries}...`,
                );
                OrderInvoiceHandler.browserInstance = await puppeteer.launch({
                  args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-audio-output',
                    '--single-process',
                    '--no-zygote',
                    '--disable-web-security',
                    '--disable-features=site-per-process',
                    '--disable-notifications',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-breakpad',
                    '--disable-component-extensions-with-background-pages',
                    '--memory-pressure-off',
                    '--disable-default-apps',
                    '--disable-sync',
                    '--disable-translate',
                    '--hide-scrollbars',
                    '--mute-audio',
                    '--no-first-run',
                    '--disable-plugins',
                    '--disable-plugins-discovery',
                    '--disable-preconnect',
                    '--disable-prefetch',
                    '--no-pings',
                    '--no-default-browser-check',
                    '--disable-hang-monitor',
                    '--disable-prompt-on-repost',
                    '--disable-domain-reliability',
                    '--disable-component-update',
                    '--disable-background-networking',
                    '--disable-background-downloads',
                    '--disable-add-to-shelf',
                    '--disable-client-side-phishing-detection',
                    '--disable-datasaver-prompt',
                    '--disable-desktop-notifications',
                    '--disable-device-discovery-notifications',
                    '--disable-infobars',
                    '--disable-features=TranslateUI',
                    '--disable-features=BlinkGenPropertyTrees',
                    '--disable-new-bookmark-apps',
                    '--disable-office-editing-component-app',
                    '--disable-reading-from-canvas',
                    '--disable-software-rasterizer',
                    '--disable-features=VizDisplayCompositor',
                  ],
                  executablePath:
                    process.env.PUPPETEER_EXECUTABLE_PATH ||
                    '/usr/bin/chromium',
                  headless: true,
                  defaultViewport: { width: 800, height: 600 },
                  timeout: 120000, // Increase browser launch timeout to 2 minutes
                  protocolTimeout: 120000, // Increase protocol timeout to 2 minutes
                  slowMo: 0, // Remove any artificial delays
                });
                console.log('[Browser launched successfully]');
                break; // Success, exit retry loop
              } catch (launchError) {
                retryCount++;
                console.error(
                  `[Browser launch failed] Attempt ${retryCount}:`,
                  launchError.message,
                );

                if (retryCount >= maxRetries) {
                  console.error('[Browser launch failed after all retries]');
                  // Cleanup any partial browser instance
                  await OrderInvoiceHandler.cleanupBrowserInstance();
                  throw launchError;
                }

                // Exponential backoff: wait 2^retryCount seconds
                const waitTime = Math.pow(2, retryCount) * 1000;
                console.log(`[Retrying browser launch in ${waitTime}ms]...`);
                await new Promise((resolve) => setTimeout(resolve, waitTime));
              }
            }
          } else {
            console.log('[Reusing existing browser instance]...');
          }

          browser = OrderInvoiceHandler.browserInstance;

          // Create new page with retry logic and timeout
          let pageRetryCount = 0;
          const maxPageRetries = 3;

          while (pageRetryCount < maxPageRetries) {
            try {
              console.log(
                `[Creating new page] Attempt ${pageRetryCount + 1}/${maxPageRetries}...`,
              );

              // Add timeout wrapper for page creation
              page = await Promise.race([
                browser.newPage(),
                new Promise((_, reject) =>
                  setTimeout(() => reject(new Error('Page creation timeout after 60 seconds')), 60000)
                )
              ]);

              console.log('[Page created successfully]');
              break;
            } catch (pageError) {
              pageRetryCount++;
              console.error(
                `[Page creation failed] Attempt ${pageRetryCount}:`,
                pageError.message,
              );

              if (pageRetryCount >= maxPageRetries) {
                console.error('[Page creation failed after all retries]');
                // Force browser restart if page creation consistently fails
                console.log('[Forcing browser restart due to page creation failures]...');
                await OrderInvoiceHandler.cleanupBrowserInstance();
                throw pageError;
              }

              // Exponential backoff for page creation retry
              const waitTime = Math.pow(2, pageRetryCount) * 2000;
              console.log(`[Retrying page creation in ${waitTime}ms]...`);
              await new Promise((resolve) =>
                setTimeout(resolve, waitTime),
              );
            }
          }

          // Set smaller viewport to reduce memory usage
          await page.setViewport({
            width: 800,
            height: 600,
            deviceScaleFactor: 1,
          });

          // Simplified request interception - block semua external resources karena images sudah base64
          await page.setRequestInterception(true);
          page.on('request', (req) => {
            const resourceType = req.resourceType();
            const url = req.url();

            // Allow base64 images dan fonts
            if (url.startsWith('data:')) {
              req.continue();
              return;
            }

            // Allow fonts dari Google Fonts
            if (
              resourceType === 'font' ||
              url.includes('fonts.googleapis.com') ||
              url.includes('fonts.gstatic.com')
            ) {
              req.continue();
              return;
            }

            // Block semua resource lainnya karena images sudah base64
            if (
              [
                'image',
                'media',
                'script',
                'websocket',
                'manifest',
                'other',
              ].includes(resourceType) ||
              url.includes('analytics') ||
              url.includes('tracking') ||
              url.includes('ads')
            ) {
              req.abort();
            } else {
              req.continue();
            }
          });

          // Disable additional features to save resources
          await page.setJavaScriptEnabled(false);
          await page.setCacheEnabled(false);

          // Simplified optimization (remove heavy DOM manipulation)
          await page.evaluate(() => {
            // Disable animations untuk performa lebih baik
            const style = document.createElement('style');
            style.textContent =
              '* { animation: none !important; transition: none !important; }';
            document.head.appendChild(style);
          });

          // Force garbage collection if possible
          if (global.gc) {
            global.gc();
          }

          // Load content dengan timeout yang cukup untuk dev environment
          try {
            console.log('[Setting page content]...');
            await page.setContent(htmlContent, {
              waitUntil: 'domcontentloaded',
              timeout: 120000, // Increase content loading timeout to 2 minutes
            });
            console.log('[Page content set successfully]');
          } catch (contentError) {
            console.error(
              '[Failed to set page content]:',
              contentError.message,
            );
            throw contentError;
          }

          // Wait untuk fonts dan rendering
          console.log('[Waiting for fonts and rendering]...');
          await new Promise((resolve) => setTimeout(resolve, 3000)); // Wait longer for dev environment
          console.log('[Fonts and rendering wait completed]');

          console.log('[Base64 images loaded, no network requests needed]');

          // Optimize PDF generation with minimal settings
          console.log('[Starting PDF generation]...');
          const pdfStartTime = Date.now();

          const pdfBuffer = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
              top: '0.4in',
              right: '0.4in',
              bottom: '0.4in',
              left: '0.4in',
            },
            preferCSSPageSize: true,
            omitBackground: false,
            timeout: 180000, // Increase PDF generation timeout to 3 minutes
            // Additional optimizations
            displayHeaderFooter: false,
            tagged: false,
            outline: false,
          });

          const pdfGenerationTime = Date.now() - pdfStartTime;
          console.log(
            `[PDF generation completed] Time: ${pdfGenerationTime}ms`,
          );

          // Check if PDF buffer is empty
          if (!pdfBuffer || pdfBuffer.length === 0) {
            console.error('[PDF creation failed]: Buffer is empty');
            throw new Error('PDF generation failed: Empty buffer');
          }

          console.log(
            '[PDF created successfully] Buffer size:',
            pdfBuffer.length,
          );

          console.log('[PDF generated successfully]');

          return {
            buffer: pdfBuffer,
            filename: `Order-${item.invoice}.pdf`,
            contentType: 'application/pdf',
          };
        } catch (error) {
          console.error('[Puppeteer error]:', error);
          throw error;
        } finally {
          try {
            if (page) {
              console.log('[Cleaning up page resources]...');
              // Clear page resources before closing
              await page.removeAllListeners();
              await page.close();
              console.log('[Page closed successfully]');
            }
          } catch (closeError) {
            console.error('[Error closing page]:', closeError);
          }

          // Don't close browser instance - keep it for reuse
          // Only force garbage collection
          try {
            if (global.gc) {
              console.log('[Running garbage collection]...');
              global.gc();
              console.log('[Garbage collection completed]');
            }
          } catch (gcError) {
            console.error('[Error during garbage collection]:', gcError);
          }
        }
      } catch (error) {
        console.error('Error in API calls:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error generating or sending email:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to generate invoice');
    }
  }

  private numberToLocaleStringID(value: number) {
    return value.toLocaleString('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });
  }

  private formatNumberToFourGroups(numberInput: string): string {
    if (!numberInput) return '';

    try {
      const cleanedNumber = numberInput.replace(/\D/g, '');

      if (cleanedNumber.length !== 15 && cleanedNumber.length !== 16) {
        console.warn(
          `Number length is not suitable for formatting. It should be 15 or 16 digits but got ${cleanedNumber.length}.`,
        );
        return numberInput; // Return original if format doesn't match
      }

      const group1 = cleanedNumber.substring(0, 2);
      const group2 = cleanedNumber.substring(2, 5);
      const group3 = cleanedNumber.substring(5, 8);
      const group4 = cleanedNumber.substring(8, 9);
      const group5 = cleanedNumber.substring(9, 12);
      const group6 = cleanedNumber.substring(12);

      return `${group1}.${group2}.${group3}.${group4}-${group5}.${group6}`;
    } catch (error) {
      console.error('Error formatting number:', error);
      return numberInput; // Return original on error
    }
  }

  private numberToWords(num: number, lang = 'en') {
    if (num === 0) return lang === 'en' ? 'zero' : 'nol';

    const words = {
      en: {
        ones: [
          '',
          'one',
          'two',
          'three',
          'four',
          'five',
          'six',
          'seven',
          'eight',
          'nine',
        ],
        teens: [
          '',
          'eleven',
          'twelve',
          'thirteen',
          'fourteen',
          'fifteen',
          'sixteen',
          'seventeen',
          'eighteen',
          'nineteen',
        ],
        tens: [
          '',
          'ten',
          'twenty',
          'thirty',
          'forty',
          'fifty',
          'sixty',
          'seventy',
          'eighty',
          'ninety',
        ],
        thousands: ['', 'thousand', 'million', 'billion'],
        hundred: 'hundred',
      },
      id: {
        ones: [
          '',
          'satu',
          'dua',
          'tiga',
          'empat',
          'lima',
          'enam',
          'tujuh',
          'delapan',
          'sembilan',
        ],
        teens: [
          '',
          'sebelas',
          'dua belas',
          'tiga belas',
          'empat belas',
          'lima belas',
          'enam belas',
          'tujuh belas',
          'delapan belas',
          'sembilan belas',
        ],
        tens: [
          '',
          'sepuluh',
          'dua puluh',
          'tiga puluh',
          'empat puluh',
          'lima puluh',
          'enam puluh',
          'tujuh puluh',
          'delapan puluh',
          'sembilan puluh',
        ],
        thousands: ['', 'ribu', 'juta', 'miliar'],
        hundred: 'ratus',
      },
    };

    function convertChunk(n, lang) {
      let str = '';
      const w = words[lang];

      if (n >= 100) {
        str += `${w.ones[Math.floor(n / 100)]} ${w.hundred} `;
        n %= 100;
      }

      if (n >= 11 && n <= 19) {
        str += `${w.teens[n - 10]} `;
        return str.trim();
      }

      if (n >= 10 || n === 10) {
        str += `${w.tens[Math.floor(n / 10)]} `;
        n %= 10;
      }

      if (n > 0) {
        str += `${w.ones[n]} `;
      }

      return str.trim();
    }

    let result = '';
    let chunkIndex = 0;

    while (num > 0) {
      const chunk = num % 1000;

      if (chunk !== 0) {
        const chunkWords = convertChunk(chunk, lang);
        result = `${chunkWords + (words[lang].thousands[chunkIndex] ? ` ${words[lang].thousands[chunkIndex]}` : '')} ${result}`;
      }

      num = Math.floor(num / 1000);
      chunkIndex++;
    }

    return result.trim();
  }
}
